import { formRequest, rbacRequest } from '@/request';
import { Unit, User } from './system';

/**
 * API 响应格式
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
};

/**
 * 字典数据项
 */
export interface DictionaryItem {
  /** 子项列表 */
  children: DictionaryItem[];
  /** 字典编码 */
  code: string;
  /** 父级编码 */
  fatherCode: string;
  /** 过滤值 */
  filterVal: string;
  /** 唯一标识 */
  id: string;
  /** 是否为子项 */
  isSon: boolean;
  /** 是否停用 */
  isStop: boolean;
  /** 组织ID */
  orgId: string;
  /** 枚举值 */
  remark: string;
  /** 排序 */
  sort: number;
  /** 显示文本 */
  text: string;
}

/**
 * 基础API服务
 */
export default {
  /** 获取字典数据  */
  getDictionary: async (code: string) => {
    const resp: ApiResponse<DictionaryItem[]> = await formRequest.get(
      `/dataDictionary/getDic/${code}`,
    );
    return resp.data;
  },

  /** 获取组织机构树形结构数据 */
  getUnitTree: async () => {
    const resp: ApiResponse<Unit> = await rbacRequest.get('/sys/unit/unitTree');
    return resp.data;
  },

  /** 获取用户列表 */
  getUserList: async (unitId: string) => {
    const resp: ApiResponse<User[]> = await rbacRequest.get('/sys/user/userList', {
      params: { unitId },
    });
    return resp.data;
  },
};
