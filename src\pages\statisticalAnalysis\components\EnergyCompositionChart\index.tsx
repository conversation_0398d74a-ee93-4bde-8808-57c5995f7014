import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

/**
 * 企业能源构成图表组件属性接口
 */
interface EnergyCompositionChartProps {
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 企业能源构成图表组件
 *
 * 显示企业各类能源（煤炭、电力、天然气等）在不同时间的构成比例
 * 通常使用堆叠柱状图展示，可以看出能源结构的变化趋势
 * 点击"更多"按钮可查看详细的月度能源构成数据
 *
 * @param props - 组件属性
 * @returns 企业能源构成图表组件
 */
const EnergyCompositionChart: React.FC<EnergyCompositionChartProps> = ({ className }) => {
  // 模拟详细数据 - 各月份不同能源类型的占比
  const detailData = [
    { key: '1', month: '2024-01', coal: 45.2, electricity: 32.1, gas: 15.3, others: 7.4 },
    { key: '2', month: '2024-02', coal: 43.8, electricity: 33.5, gas: 16.1, others: 6.6 },
    { key: '3', month: '2024-03', coal: 46.1, electricity: 31.2, gas: 14.9, others: 7.8 },
    { key: '4', month: '2024-04', coal: 44.5, electricity: 32.8, gas: 15.7, others: 7.0 },
    { key: '5', month: '2024-05', coal: 45.9, electricity: 31.9, gas: 15.2, others: 7.0 },
    { key: '6', month: '2024-06', coal: 44.7, electricity: 32.4, gas: 15.8, others: 7.1 },
  ];

  const columns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: '煤炭 (%)',
      dataIndex: 'coal',
      key: 'coal',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: '电力 (%)',
      dataIndex: 'electricity',
      key: 'electricity',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: '天然气 (%)',
      dataIndex: 'gas',
      key: 'gas',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: '其他 (%)',
      dataIndex: 'others',
      key: 'others',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是企业能源构成的详细数据，显示各类能源在不同月份的占比：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );

  return (
    <ChartCard
      title="企业能源构成"
      className={className}
      modalTitle="企业能源构成 - 详细数据"
      modalContent={modalContent}
    >
      {/* 堆叠柱状图占位 - 这里将来会替换为真实的图表组件 */}
      <span>堆叠柱状图 - 企业能源构成</span>
    </ChartCard>
  );
};

export default EnergyCompositionChart;
