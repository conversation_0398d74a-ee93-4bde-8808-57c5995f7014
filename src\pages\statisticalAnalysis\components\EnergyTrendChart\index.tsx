import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

interface EnergyTrendChartProps {
  className?: string;
}

const EnergyTrendChart: React.FC<EnergyTrendChartProps> = ({ className }) => {
  // 模拟详细数据
  const detailData = [
    { key: '1', month: '2024-01', energy: 1420, unit: '万tce' },
    { key: '2', month: '2024-02', energy: 1380, unit: '万tce' },
    { key: '3', month: '2024-03', energy: 1450, unit: '万tce' },
    { key: '4', month: '2024-04', energy: 1390, unit: '万tce' },
    { key: '5', month: '2024-05', energy: 1410, unit: '万tce' },
    { key: '6', month: '2024-06', energy: 1384, unit: '万tce' },
  ];

  const columns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: '能耗量',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => `${value.toLocaleString()}`,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能耗趋势的详细数据，包含各月份的具体能耗量：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );

  return (
    <ChartCard
      title="能耗趋势"
      className={className}
      modalTitle="能耗趋势 - 详细数据"
      modalContent={modalContent}
    >
      {/* 折线图占位 - 这里将来会替换为真实的图表组件 */}
      <span>折线图 - 能耗趋势</span>
    </ChartCard>
  );
};

export default EnergyTrendChart;
