import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

interface ProductEnergyChartProps {
  className?: string;
}

const ProductEnergyChart: React.FC<ProductEnergyChartProps> = ({ className }) => {
  // 模拟详细数据
  const detailData = [
    { key: '1', product: '产品A', energy: 2.45, average: 2.34, unit: 'tce/万元' },
    { key: '2', product: '产品B', energy: 2.12, average: 2.34, unit: 'tce/万元' },
    { key: '3', product: '产品C', energy: 2.67, average: 2.34, unit: 'tce/万元' },
    { key: '4', product: '产品D', energy: 2.23, average: 2.34, unit: 'tce/万元' },
    { key: '5', product: '产品E', energy: 2.38, average: 2.34, unit: 'tce/万元' },
  ];

  const columns = [
    {
      title: '产品名称',
      dataIndex: 'product',
      key: 'product',
    },
    {
      title: '单品能耗',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '平均值',
      dataIndex: 'average',
      key: 'average',
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '与平均值对比',
      key: 'comparison',
      render: (record: any) => {
        const diff = ((record.energy - record.average) / record.average * 100);
        const isHigher = diff > 0;
        return (
          <span style={{ color: isHigher ? '#ff4d4f' : '#52c41a' }}>
            {isHigher ? '+' : ''}{diff.toFixed(1)}%
          </span>
        );
      },
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是各产品单品能耗的详细数据，包含与平均值的对比：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );

  return (
    <ChartCard
      title="单品能耗"
      className={className}
      modalTitle="单品能耗 - 详细数据"
      modalContent={modalContent}
    >
      {/* 柱状图 + 平均线占位 - 这里将来会替换为真实的图表组件 */}
      <span>柱状图 + 平均线 - 单品能耗</span>
    </ChartCard>
  );
};

export default ProductEnergyChart;
