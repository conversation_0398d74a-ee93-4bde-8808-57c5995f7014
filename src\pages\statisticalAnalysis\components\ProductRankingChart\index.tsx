import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

interface ProductRankingChartProps {
  className?: string;
}

const ProductRankingChart: React.FC<ProductRankingChartProps> = ({ className }) => {
  // 模拟详细数据
  const detailData = [
    { key: '1', rank: 1, product: '产品C', energy: 2.67, percentage: 18.5 },
    { key: '2', rank: 2, product: '产品A', energy: 2.45, percentage: 16.9 },
    { key: '3', rank: 3, product: '产品E', energy: 2.38, percentage: 16.4 },
    { key: '4', rank: 4, product: '产品D', energy: 2.23, percentage: 15.4 },
    { key: '5', rank: 5, product: '产品B', energy: 2.12, percentage: 14.6 },
    { key: '6', rank: 6, product: '产品F', energy: 1.98, percentage: 13.7 },
    { key: '7', rank: 7, product: '产品G', energy: 1.85, percentage: 12.8 },
    { key: '8', rank: 8, product: '产品H', energy: 1.72, percentage: 11.9 },
  ];

  const columns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      render: (rank: number) => {
        let color = '#595959';
        if (rank === 1) color = '#ff4d4f';
        else if (rank === 2) color = '#fa8c16';
        else if (rank === 3) color = '#faad14';
        
        return <span style={{ color, fontWeight: 600 }}>{rank}</span>;
      },
    },
    {
      title: '产品名称',
      dataIndex: 'product',
      key: 'product',
    },
    {
      title: '单品能耗',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => `${value.toFixed(2)} tce/万元`,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是产品能耗排行的详细数据，按单品能耗从高到低排序：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );

  return (
    <ChartCard
      title="单品排行"
      className={className}
      modalTitle="单品排行 - 详细数据"
      modalContent={modalContent}
    >
      {/* 横向条形图占位 - 这里将来会替换为真实的图表组件 */}
      <span>横向条形图 - 单品排行</span>
    </ChartCard>
  );
};

export default ProductRankingChart;
