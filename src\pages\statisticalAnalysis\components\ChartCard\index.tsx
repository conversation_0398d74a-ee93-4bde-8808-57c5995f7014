import * as React from 'react';
import { Modal } from 'antd';
import style from './index.module.less';

/**
 * 图表卡片组件属性接口
 */
interface ChartCardProps {
  /** 图表标题 */
  title: string;
  /** 图表内容（通常是图表组件） */
  children: React.ReactNode;
  /** 自定义"更多"按钮点击事件，如果不提供则使用默认的Modal展示 */
  onMoreClick?: () => void;
  /** Modal标题，默认为"{title} - 详细数据" */
  modalTitle?: string;
  /** Modal内容，如果不提供则显示占位内容 */
  modalContent?: React.ReactNode;
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 通用图表卡片组件
 *
 * 提供统一的图表容器，包含：
 * - 标题栏（标题 + "更多"按钮）
 * - 图表内容区
 * - 详细数据Modal
 *
 * @param props - 组件属性
 * @returns 图表卡片组件
 */
const ChartCard: React.FC<ChartCardProps> = ({
  title,
  children,
  onMoreClick,
  modalTitle,
  modalContent,
  className,
}) => {
  // Modal显示状态
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick = () => {
    if (onMoreClick) {
      // 如果提供了自定义点击处理函数，则调用它
      onMoreClick();
    } else {
      // 否则显示默认的详细数据Modal
      setIsModalVisible(true);
    }
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <div className={`${style['chart-card']} ${className || ''}`}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>{title}</div>
          <div className={style['chart-more']} onClick={handleMoreClick}>
            更多
          </div>
        </div>
        
        {/* 图表内容区 */}
        <div className={style['chart-content']}>
          {children}
        </div>
      </div>

      {/* 详细数据Modal */}
      <Modal
        title={modalTitle || `${title} - 详细数据`}
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent || (
          <div className={style['modal-placeholder']}>
            <p>详细数据展示区域</p>
            <p>这里将显示 {title} 的详细数据表格或更多图表</p>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ChartCard;
