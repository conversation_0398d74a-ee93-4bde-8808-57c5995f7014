import * as React from 'react';
import { Select } from 'antd';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import style from './index.module.less';

// 时间维度类型
type TimeDimension = 'year' | 'month';

// 统计周期类型
type StatisticsPeriod = 'recent3Years' | 'recent12Months' | 'recent6Months';

/**
 * 统计分析
 * @returns React.ReactNode
 */
const StatisticalAnalysis: React.FC = () => {
  // 时间维度状态
  const [timeDimension, setTimeDimension] = React.useState<TimeDimension>('month');

  // 统计周期状态
  const [statisticsPeriod, setStatisticsPeriod] =
    React.useState<StatisticsPeriod>('recent12Months');

  // 时间维度选项
  const timeDimensionOptions: { value: TimeDimension; label: string }[] = [
    { value: 'year', label: '年度统计' },
    { value: 'month', label: '月度统计' },
  ];

  // 根据时间维度获取统计周期选项
  const getStatisticsPeriodOptions: {
    value: StatisticsPeriod;
    label: string;
  }[] = React.useMemo(() => {
    if (timeDimension === 'year') {
      return [{ value: 'recent3Years', label: '近3年' }];
    } else {
      return [
        { value: 'recent12Months', label: '近12个月' },
        { value: 'recent6Months', label: '近6个月' },
      ];
    }
  }, [timeDimension]);

  // 处理时间维度变更
  const handleTimeDimensionChange = (value: TimeDimension) => {
    setTimeDimension(value);
    // 根据时间维度自动设置默认的统计周期
    if (value === 'year') {
      setStatisticsPeriod('recent3Years');
    } else {
      setStatisticsPeriod('recent12Months');
    }
    // TODO: 根据时间维度更新数据
  };

  // 处理统计周期变更
  const handleStatisticsPeriodChange = (value: StatisticsPeriod) => {
    setStatisticsPeriod(value);
    // TODO: 根据统计周期更新数据
  };

  // 指标卡片数据
  const indicatorData = [
    {
      id: 1,
      value: '1,384',
      unit: '万tce',
      title: '平均综合能耗（单月）',
      tooltip: '统计周期内，各月综合能耗的算术平均值',
      trend: { type: 'up', value: '+5.6%' },
      icon: (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
          <path
            d="M3 17L9 11L13 15L21 7"
            stroke="#d9d9d9"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <circle cx="3" cy="17" r="1" fill="#d9d9d9" />
          <circle cx="9" cy="11" r="1" fill="#d9d9d9" />
          <circle cx="13" cy="15" r="1" fill="#d9d9d9" />
          <circle cx="21" cy="7" r="1" fill="#d9d9d9" />
        </svg>
      ),
    },
    {
      id: 2,
      value: '892',
      unit: '万元',
      title: '平均能源成本（单月）',
      tooltip: '统计周期内，各月能源采购成本的算术平均值',
      trend: { type: 'down', value: '-2.3%' },
      icon: (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
          <rect
            x="3"
            y="3"
            width="18"
            height="18"
            rx="2"
            stroke="#d9d9d9"
            strokeWidth="2"
            fill="none"
          />
          <path d="M9 9L15 15M15 9L9 15" stroke="#d9d9d9" strokeWidth="2" strokeLinecap="round" />
        </svg>
      ),
    },
    {
      id: 3,
      value: '76.8',
      unit: '%',
      title: '能源利用效率',
      tooltip: '有效能源利用量占总能源投入量的百分比',
      trend: { type: 'up', value: '+1.2%' },
      icon: (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 2L2 7L12 12L22 7L12 2Z"
            stroke="#d9d9d9"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M2 17L12 22L22 17"
            stroke="#d9d9d9"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M2 12L12 17L22 12"
            stroke="#d9d9d9"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
    },
    {
      id: 4,
      value: '2.34',
      unit: 'tce/万元',
      title: '单位产值能耗',
      tooltip: '每万元产值所消耗的标准煤当量',
      trend: { type: 'down', value: '-3.1%' },
      icon: (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="10" stroke="#d9d9d9" strokeWidth="2" fill="none" />
          <path d="M12 6V12L16 14" stroke="#d9d9d9" strokeWidth="2" strokeLinecap="round" />
        </svg>
      ),
    },
  ];

  return (
    <div className={style['statistical-analysis']}>
      {/* 筛选与导出区（顶部） */}
      <div className={style['filter-export-section']}>
        {/* 左侧：时间维度和统计周期 */}
        <div className={style['filter-controls']}>
          <div className={style['time-dimension']}>
            {/* 时间维度选择 */}
            <span className={style.label}>时间维度：</span>
            <Select
              value={timeDimension}
              onChange={handleTimeDimensionChange}
              options={timeDimensionOptions}
              style={{ width: 120 }}
              size="middle"
            />
          </div>
          <div className={style['period-selection']}>
            {/* 统计周期选择 */}
            <span className={style.label}>统计周期：</span>
            <Select
              value={statisticsPeriod}
              onChange={handleStatisticsPeriodChange}
              options={getStatisticsPeriodOptions}
              style={{ width: 120 }}
              size="middle"
            />
          </div>
        </div>

        {/* 右侧：月报导出 */}
        <div className={style['export-button']}>
          {/* 月报导出按钮 */}
          <span>月报导出</span>
        </div>
      </div>

      {/* 关键指标展示区（顶部下方，卡片式） */}
      <div className={style['key-indicators-section']}>
        {indicatorData.map((indicator) => (
          <div key={indicator.id} className={style['indicator-card']}>
            {/* 右侧图标 */}
            <div className={style['card-icon']}>{indicator.icon}</div>

            {/* 最上层：核心数值 */}
            <div className={style['card-value']}>
              <span className={style['value-number']}>{indicator.value}</span>
              <span className={style['value-unit']}>{indicator.unit}</span>
            </div>

            {/* 中间层：指标定义 + 交互提示 */}
            <div className={style['card-title']}>
              <span className={style['title-text']}>{indicator.title}</span>
            </div>

            {/* 最下层：趋势对比 */}
            <div className={style['card-trend']}>
              <span
                className={indicator.trend.type === 'up' ? style['trend-up'] : style['trend-down']}
              >
                <span className={style['trend-arrow']}>
                  {indicator.trend.type === 'up' ? '↗' : '↘'}
                </span>
                <span className={style['trend-text']}>同比 {indicator.trend.value}</span>
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* 数据可视化区（中间，分两排） */}
      <div className={style['visualization-section']}>
        {/* 第一排：能耗趋势（折线）、单品能耗（柱状 + 平均线） */}
        <div className={style['chart-row']}>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>能耗趋势</div>
            <div className={style['chart-content']}>
              {/* 折线图占位 */}
              <span>折线图 - 能耗趋势</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>单品能耗</div>
            <div className={style['chart-content']}>
              {/* 柱状图 + 平均线占位 */}
              <span>柱状图 + 平均线 - 单品能耗</span>
            </div>
          </div>
        </div>

        {/* 第二排：企业能源构成（堆叠柱）、单品排行（横向条）、能源结构（饼图） */}
        <div className={style['chart-row']}>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>企业能源构成</div>
            <div className={style['chart-content']}>
              {/* 堆叠柱状图占位 */}
              <span>堆叠柱状图 - 企业能源构成</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>单品排行</div>
            <div className={style['chart-content']}>
              {/* 横向条形图占位 */}
              <span>横向条形图 - 单品排行</span>
            </div>
          </div>
          <div className={style['chart-container']}>
            <div className={style['chart-title']}>能源结构</div>
            <div className={style['chart-content']}>
              {/* 饼图占位 */}
              <span>饼图 - 能源结构</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YTHLocalization.withLocal(
  StatisticalAnalysis,
  locales,
  YTHLocalization.getLanguage(),
);
