import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

/**
 * 单品能耗图表组件属性接口
 */
interface ProductEnergyChartProps {
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 单品能耗图表组件
 *
 * 显示各产品的单位能耗情况，通常使用柱状图+平均线展示
 * 可以直观看出哪些产品的能耗高于或低于平均水平
 * 点击"更多"按钮可查看详细的产品能耗对比数据
 *
 * @param props - 组件属性
 * @returns 单品能耗图表组件
 */
const ProductEnergyChart: React.FC<ProductEnergyChartProps> = ({ className }) => {
  // 模拟详细数据 - 各产品单品能耗及与平均值对比
  const detailData = [
    { key: '1', product: '产品A', energy: 2.45, average: 2.34, unit: 'tce/万元' },
    { key: '2', product: '产品B', energy: 2.12, average: 2.34, unit: 'tce/万元' },
    { key: '3', product: '产品C', energy: 2.67, average: 2.34, unit: 'tce/万元' },
    { key: '4', product: '产品D', energy: 2.23, average: 2.34, unit: 'tce/万元' },
    { key: '5', product: '产品E', energy: 2.38, average: 2.34, unit: 'tce/万元' },
  ];

  // 详细数据表格列配置
  const columns = [
    {
      title: '产品名称',
      dataIndex: 'product',
      key: 'product',
    },
    {
      title: '单品能耗',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => value.toFixed(2), // 保留2位小数
    },
    {
      title: '平均值',
      dataIndex: 'average',
      key: 'average',
      render: (value: number) => value.toFixed(2), // 保留2位小数
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '与平均值对比',
      key: 'comparison',
      render: (record: any) => {
        // 计算与平均值的差异百分比
        const diff = ((record.energy - record.average) / record.average * 100);
        const isHigher = diff > 0;
        return (
          <span style={{ color: isHigher ? '#ff4d4f' : '#52c41a' }}>
            {isHigher ? '+' : ''}{diff.toFixed(1)}%
          </span>
        );
      },
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是各产品单品能耗的详细数据，包含与平均值的对比：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );

  return (
    <ChartCard
      title="单品能耗"
      className={className}
      modalTitle="单品能耗 - 详细数据"
      modalContent={modalContent}
    >
      {/* 柱状图 + 平均线占位 - 这里将来会替换为真实的图表组件 */}
      <span>柱状图 + 平均线 - 单品能耗</span>
    </ChartCard>
  );
};

export default ProductEnergyChart;
