import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

/**
 * 能耗趋势图表组件属性接口
 */
interface EnergyTrendChartProps {
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 能耗趋势图表组件
 *
 * 显示企业能耗随时间变化的趋势，通常使用折线图展示
 * 点击"更多"按钮可查看详细的月度能耗数据表格
 *
 * @param props - 组件属性
 * @returns 能耗趋势图表组件
 */
const EnergyTrendChart: React.FC<EnergyTrendChartProps> = ({ className }) => {
  // 模拟详细数据 - 各月份能耗量
  const detailData = [
    { key: '1', month: '2024-01', energy: 1420, unit: '万tce' },
    { key: '2', month: '2024-02', energy: 1380, unit: '万tce' },
    { key: '3', month: '2024-03', energy: 1450, unit: '万tce' },
    { key: '4', month: '2024-04', energy: 1390, unit: '万tce' },
    { key: '5', month: '2024-05', energy: 1410, unit: '万tce' },
    { key: '6', month: '2024-06', energy: 1384, unit: '万tce' },
  ];

  // 详细数据表格列配置
  const columns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: '能耗量',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => `${value.toLocaleString()}`, // 格式化数字显示
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能耗趋势的详细数据，包含各月份的具体能耗量：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );

  return (
    <ChartCard
      title="能耗趋势"
      className={className}
      modalTitle="能耗趋势 - 详细数据"
      modalContent={modalContent}
    >
      {/* 折线图占位 - 这里将来会替换为真实的图表组件 */}
      <span>折线图 - 能耗趋势</span>
    </ChartCard>
  );
};

export default EnergyTrendChart;
