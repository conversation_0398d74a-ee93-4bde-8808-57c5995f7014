import * as React from 'react';
import { Table } from 'antd';
import ChartCard from '../ChartCard';

interface EnergyStructureChartProps {
  className?: string;
}

const EnergyStructureChart: React.FC<EnergyStructureChartProps> = ({ className }) => {
  // 模拟详细数据
  const detailData = [
    { key: '1', energyType: '煤炭', amount: 621.8, percentage: 44.9, unit: '万tce' },
    { key: '2', energyType: '电力', amount: 447.7, percentage: 32.3, unit: '万tce' },
    { key: '3', energyType: '天然气', amount: 216.4, percentage: 15.6, unit: '万tce' },
    { key: '4', energyType: '石油', amount: 69.2, percentage: 5.0, unit: '万tce' },
    { key: '5', energyType: '其他', amount: 28.9, percentage: 2.1, unit: '万tce' },
  ];

  const columns = [
    {
      title: '能源类型',
      dataIndex: 'energyType',
      key: 'energyType',
      render: (text: string, record: any) => {
        // 为不同能源类型添加颜色标识
        const colors: { [key: string]: string } = {
          '煤炭': '#8c8c8c',
          '电力': '#1890ff',
          '天然气': '#52c41a',
          '石油': '#fa8c16',
          '其他': '#722ed1',
        };
        
        return (
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <span
              style={{
                width: 12,
                height: 12,
                backgroundColor: colors[text] || '#d9d9d9',
                borderRadius: '50%',
                marginRight: 8,
              }}
            />
            {text}
          </span>
        );
      },
    },
    {
      title: '消耗量',
      dataIndex: 'amount',
      key: 'amount',
      render: (value: number, record: any) => `${value.toFixed(1)} ${record.unit}`,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能源结构的详细数据，显示各类能源的消耗量和占比：
      </p>
      <Table
        columns={columns}
        dataSource={detailData}
        pagination={false}
        size="small"
        bordered
      />
      <div style={{ marginTop: 16, fontSize: 12, color: '#8c8c8c' }}>
        * 数据统计周期：当前选择的时间范围内的累计消耗量
      </div>
    </div>
  );

  return (
    <ChartCard
      title="能源结构"
      className={className}
      modalTitle="能源结构 - 详细数据"
      modalContent={modalContent}
    >
      {/* 饼图占位 - 这里将来会替换为真实的图表组件 */}
      <span>饼图 - 能源结构</span>
    </ChartCard>
  );
};

export default EnergyStructureChart;
