import * as React from 'react';
import { Modal } from 'antd';
import style from './index.module.less';

interface ChartCardProps {
  title: string;
  children: React.ReactNode;
  onMoreClick?: () => void;
  modalTitle?: string;
  modalContent?: React.ReactNode;
  className?: string;
}

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  children,
  onMoreClick,
  modalTitle,
  modalContent,
  className,
}) => {
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  const handleMoreClick = () => {
    if (onMoreClick) {
      onMoreClick();
    } else {
      setIsModalVisible(true);
    }
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <div className={`${style['chart-card']} ${className || ''}`}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>{title}</div>
          <div className={style['chart-more']} onClick={handleMoreClick}>
            更多
          </div>
        </div>
        
        {/* 图表内容区 */}
        <div className={style['chart-content']}>
          {children}
        </div>
      </div>

      {/* 详细数据Modal */}
      <Modal
        title={modalTitle || `${title} - 详细数据`}
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent || (
          <div className={style['modal-placeholder']}>
            <p>详细数据展示区域</p>
            <p>这里将显示 {title} 的详细数据表格或更多图表</p>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ChartCard;
